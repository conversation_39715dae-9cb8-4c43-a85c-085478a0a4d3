'use client';
import type { User } from 'database';
import { usePathname } from 'next/navigation';
import { Sidebar as DesktopSidebar } from '@saas/shared/components/navigation/Sidebar';
import { MobileHeader } from '@saas/shared/components/navigation/MobileHeader';
import { MobileNavbar } from '@saas/shared/components/navigation/MobileNavbar';
import { cx } from 'class-variance-authority';
import React from 'react';
import { OnlineStatusProvider } from '@saas/shared/contexts/OnlineStatusContext';

export default function LayoutClient({
	children,
	user,
}: Readonly<{
	children: React.ReactNode;
	user: User;
}>) {
	const [isCollapsed, setIsCollapsed] = React.useState(false);
	const pathname = usePathname();
	const isChatRoute = pathname?.includes('/zapchat');

	const toggleSidebar = () => {
		setIsCollapsed(!isCollapsed);
	};

	return (
		<OnlineStatusProvider>
			<div className='mx-auto w-full'>
				<DesktopSidebar
					isCollapsed={isCollapsed}
					user={user}
					toggleSidebar={toggleSidebar}
				/>

				{/* Mobile Header - visível apenas em dispositivos móveis */}
				<MobileHeader />

				<main
					className={cx(
						isCollapsed ? 'lg:pl-[60px]' : 'lg:pl-64',
						'ease transform-gpu transition-all duration-100 will-change-transform lg:bg-gray-50 lg:py-3 lg:pr-3 lg:dark:bg-gray-950'
					)}
				>
					{isChatRoute ? (
						<div className='h-screen overflow-hidden'>
							{children}
						</div>
					) : (
						<div className='bg-white min-h-[calc(100vh-1rem)] p-4 pt-2 pb-20 sm:pb-4 sm:p-6 lg:rounded-lg lg:border lg:border-gray-200 dark:bg-gray-925 lg:dark:border-gray-900'>
							{children}
						</div>
					)}
				</main>

				{/* Mobile Navbar - visível apenas em dispositivos móveis e não visível na página de chat */}
				{!isChatRoute && <MobileNavbar />}
			</div>
		</OnlineStatusProvider>
	);
}
