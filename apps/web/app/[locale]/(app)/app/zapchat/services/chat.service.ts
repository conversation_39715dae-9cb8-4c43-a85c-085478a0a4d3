import { createRealtimeClient } from "@shared/lib/supabase/realtime-client";
import { Message } from "database";
import { v4 as uuidv4 } from "uuid";

export interface ChatMessage extends Message {
  id: string;
  appointmentId: string;
  senderId: string;
  senderRole: 'DOCTOR' | 'PATIENT' | 'ADMIN';
  type: 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM';
  content: string;
  metadata?: Record<string, any>;
  createdAt: string;
}

export interface ChatServiceConfig {
  appointmentId: string;
  userId: string;
  userRole: 'DOCTOR' | 'PATIENT' | 'ADMIN';
  onMessage?: (message: ChatMessage) => void;
  onError?: (error: Error) => void;
  onConnectionChange?: (connected: boolean) => void;
}

export class ChatService {
  private supabaseClient = createRealtimeClient();
  private channel: any = null;
  private config: ChatServiceConfig;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  constructor(config: ChatServiceConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    try {
      await this.disconnect();

      const channelName = `appointment:${this.config.appointmentId}`;
      this.channel = this.supabaseClient.channel(channelName);

      this.channel
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `appointment_id=eq.${this.config.appointmentId}`
          },
          (payload: any) => {
            const message = this.transformMessage(payload.new);
            if (message && this.config.onMessage) {
              this.config.onMessage(message);
            }
          }
        )
        .subscribe((status: string) => {
          this.handleConnectionStatus(status);
        });

    } catch (error) {
      console.error('Erro ao conectar chat:', error);
      this.config.onError?.(error as Error);
      this.scheduleReconnect();
    }
  }

  async disconnect(): Promise<void> {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.channel) {
      try {
        await this.supabaseClient.removeChannel(this.channel);
      } catch (error) {
        console.error('Erro ao desconectar canal:', error);
      }
      this.channel = null;
    }

    this.isConnected = false;
    this.config.onConnectionChange?.(false);
  }

  async getMessages(): Promise<ChatMessage[]> {
    try {
      const { data, error } = await this.supabaseClient
        .from('messages')
        .select('*')
        .eq('appointment_id', this.config.appointmentId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      return data.map(msg => this.transformMessage(msg)).filter(Boolean) as ChatMessage[];
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
      throw error;
    }
  }

  async sendTextMessage(content: string): Promise<ChatMessage> {
    if (!content.trim()) {
      throw new Error('Mensagem não pode estar vazia');
    }

    const messageData = {
      id: uuidv4(),
      appointment_id: this.config.appointmentId,
      sender_id: this.config.userId,
      sender_role: this.config.userRole,
      type: 'TEXT',
      content: content.trim(),
      created_at: new Date().toISOString(),
    };

    try {
      const { data, error } = await this.supabaseClient
        .from('messages')
        .insert(messageData)
        .select()
        .single();

      if (error) throw error;

      return this.transformMessage(data) as ChatMessage;
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  async sendAudioMessage(audioBlob: Blob): Promise<ChatMessage> {
    try {
      const fileName = `${this.config.appointmentId}/${Date.now()}.webm`;
      const arrayBuffer = await audioBlob.arrayBuffer();

      const { data: uploadData, error: uploadError } = await this.supabaseClient.storage
        .from('chat_audio')
        .upload(fileName, arrayBuffer, {
          contentType: 'audio/webm',
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = this.supabaseClient.storage
        .from('chat_audio')
        .getPublicUrl(fileName);

      const messageData = {
        id: uuidv4(),
        appointment_id: this.config.appointmentId,
        sender_id: this.config.userId,
        sender_role: this.config.userRole,
        type: 'AUDIO',
        content: publicUrl,
        metadata: {
          fileName,
          contentType: 'audio/webm',
          duration: 0,
        },
        created_at: new Date().toISOString(),
      };

      const { data, error } = await this.supabaseClient
        .from('messages')
        .insert(messageData)
        .select()
        .single();

      if (error) throw error;

      return this.transformMessage(data) as ChatMessage;
    } catch (error) {
      console.error('Erro ao enviar áudio:', error);
      throw error;
    }
  }

  async sendFileMessage(file: File): Promise<ChatMessage> {
    try {
      const fileName = `${this.config.appointmentId}/${Date.now()}_${file.name}`;
      const arrayBuffer = await file.arrayBuffer();

      const { data: uploadData, error: uploadError } = await this.supabaseClient.storage
        .from('chat_attachments')
        .upload(fileName, arrayBuffer, {
          contentType: file.type,
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = this.supabaseClient.storage
        .from('chat_attachments')
        .getPublicUrl(fileName);

      const messageData = {
        id: uuidv4(),
        appointment_id: this.config.appointmentId,
        sender_id: this.config.userId,
        sender_role: this.config.userRole,
        type: 'FILE',
        content: publicUrl,
        metadata: {
          fileName: file.name,
          contentType: file.type,
          size: file.size,
          path: fileName,
        },
        created_at: new Date().toISOString(),
      };

      const { data, error } = await this.supabaseClient
        .from('messages')
        .insert(messageData)
        .select()
        .single();

      if (error) throw error;

      return this.transformMessage(data) as ChatMessage;
    } catch (error) {
      console.error('Erro ao enviar arquivo:', error);
      throw error;
    }
  }

  private transformMessage(dbMessage: any): ChatMessage | null {
    if (!dbMessage) return null;

    return {
      id: dbMessage.id,
      appointmentId: dbMessage.appointment_id,
      senderId: dbMessage.sender_id,
      senderRole: dbMessage.sender_role,
      type: dbMessage.type,
      content: dbMessage.content,
      metadata: dbMessage.metadata,
      createdAt: dbMessage.created_at,
    };
  }

  private handleConnectionStatus(status: string): void {
    console.log(`Chat connection status: ${status}`);

    switch (status) {
      case 'SUBSCRIBED':
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.config.onConnectionChange?.(true);
        break;
      case 'CHANNEL_ERROR':
      case 'TIMED_OUT':
        this.isConnected = false;
        this.config.onConnectionChange?.(false);
        this.scheduleReconnect();
        break;
      case 'CLOSED':
        this.isConnected = false;
        this.config.onConnectionChange?.(false);
        break;
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Máximo de tentativas de reconexão atingido');
      this.config.onError?.(new Error('Falha na conexão após múltiplas tentativas'));
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    this.reconnectAttempts++;

    this.reconnectTimeout = setTimeout(() => {
      console.log(`Tentativa de reconexão ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
      this.connect();
    }, delay);
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  destroy(): void {
    this.disconnect();
  }
}
