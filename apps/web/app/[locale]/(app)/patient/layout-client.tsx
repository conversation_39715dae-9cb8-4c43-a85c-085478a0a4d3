"use client";

import type { User } from 'database';
import { PropsWithChildren, useEffect } from "react";
import { BottomNavigation } from "./components/navigation/bottom-navigation";
import { PatientHeader } from "./components/navigation/patient-header";
import { usePathname } from "next/navigation";
import { cn } from "@ui/lib";

interface PatientLayoutClientProps extends PropsWithChildren {
	user: User;
}

export function PatientLayoutClient({
	children,
	user
}: PatientLayoutClientProps) {
	const pathname = usePathname() ?? "";

	// Páginas onde não queremos mostrar header/nav (onboarding, etc)
	const isOnboarding = pathname.includes("/onboarding");
	const isFullscreen = pathname.includes("/consultation") || pathname.includes("/call");
	// Layout mais nativo para chat (sem bottom nav e header)
	const isChatPage = pathname.includes("/patient/zapchat") || pathname.includes("/patient/chat");

	useEffect(() => {
		// Adicionar classe mobile-first no body para CSS global
		document.body.classList.add("patient-portal");
		return () => {
			document.body.classList.remove("patient-portal");
		};
	}, []);

	if (isOnboarding) {
		return (
			<div className="min-h-screen bg-gray-50">
				{children}
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50 flex flex-col">
			{/* Header - sempre visível exceto em consultas fullscreen e chat */}
			{!(isFullscreen || isChatPage) && (
				<PatientHeader user={user} />
			)}

			{/* Main Content */}
			<main className={cn(
				"flex-1 flex flex-col",
				!(isFullscreen || isChatPage) && "pb-2", // Espaço para nav inferior no mobile (exceto chat)
				isChatPage && "h-screen overflow-hidden" // Full height para chat
			)}>
				{children}
			</main>

			{/* Bottom Navigation - apenas mobile */}
			{!(isFullscreen || isChatPage) && (
				<BottomNavigation />
			)}
		</div>
	);
}
