export interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey?: string;
  realtime: {
    eventsPerSecond: number;
    heartbeatInterval: number;
    connectionTimeout: number;
    maxReconnectAttempts: number;
    baseReconnectDelay: number;
  };
}

export function getSupabaseConfig(): SupabaseConfig {
  try {
    // Tentar usar a configuração do cliente primeiro
    const { getClientSupabaseConfig } = require('./client-config');
    const config = getClientSupabaseConfig();

    return {
      url: config.url,
      anonKey: config.anonKey,
      serviceRoleKey: undefined, // Não disponível no lado do cliente
      realtime: {
        eventsPerSecond: 20,
        heartbeatInterval: 30000, // 30 segundos
        connectionTimeout: 10000, // 10 segundos
        maxReconnectAttempts: 10,
        baseReconnectDelay: 1000, // 1 segundo
      }
    };
  } catch (error) {
    // Fallback para a configuração antiga
    const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!url || !anonKey) {
      throw new Error(
        'Supabase configuration missing. Please check your environment variables:\n' +
        `NEXT_PUBLIC_SUPABASE_URL: ${url ? '✓' : '✗'}\n` +
        `NEXT_PUBLIC_SUPABASE_ANON_KEY: ${anonKey ? '✓' : '✗'}`
      );
    }

    return {
      url,
      anonKey,
      serviceRoleKey: undefined, // Não disponível no lado do cliente
      realtime: {
        eventsPerSecond: 20,
        heartbeatInterval: 30000, // 30 segundos
        connectionTimeout: 10000, // 10 segundos
        maxReconnectAttempts: 10,
        baseReconnectDelay: 1000, // 1 segundo
      }
    };
  }
}

export function validateSupabaseConnection(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      const config = getSupabaseConfig();

      // Criar cliente temporário para testar conexão
      const { createClient } = require('@supabase/supabase-js');
      const testClient = createClient(config.url, config.anonKey);

      // Testar conexão com timeout
      const timeout = setTimeout(() => {
        resolve(false);
      }, 5000);

      testClient.auth.getSession().then(() => {
        clearTimeout(timeout);
        resolve(true);
      }).catch(() => {
        clearTimeout(timeout);
        resolve(false);
      });
    } catch (error) {
      console.error('[Supabase] Erro na validação de configuração:', error);
      resolve(false);
    }
  });
}
