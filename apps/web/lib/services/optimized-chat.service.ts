import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import { getSupabaseConfig } from '../config/supabase.config';

export interface ChatMessage {
  id: string;
  appointmentId: string;
  senderId: string;
  senderRole: 'DOCTOR' | 'PATIENT';
  type: 'TEXT' | 'AUDIO' | 'FILE' | 'SYSTEM';
  content: string;
  metadata?: Record<string, any>;
  createdAt: string;
  // Campos de arquivo agora estão no metadata
  file_url?: string;
  file_name?: string;
  file_size?: number;
}

export interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';
  lastConnected?: Date;
  reconnectAttempts: number;
  lastError?: string;
  isOnline: boolean;
  latency?: number;
}

export interface TypingStatus {
  userId: string;
  userName: string;
  isTyping: boolean;
  timestamp: number;
}

type MessageHandler = (message: ChatMessage) => void;
type StatusHandler = (status: ConnectionStatus) => void;
type TypingHandler = (typing: TypingStatus[]) => void;
type ErrorHandler = (error: Error) => void;

export class OptimizedChatService {
  private client: SupabaseClient;
  private channel: any = null;
  private connectionStatus: ConnectionStatus = {
    status: 'disconnected',
    reconnectAttempts: 0,
    isOnline: navigator?.onLine ?? true,
  };

  private messageHandlers: MessageHandler[] = [];
  private statusHandlers: StatusHandler[] = [];
  private typingHandlers: TypingHandler[] = [];
  private errorHandlers: ErrorHandler[] = [];

  // Controle de reconexão
  private reconnectTimeout?: NodeJS.Timeout;
  private maxReconnectAttempts = 5;
  private baseReconnectDelay = 1000;
  private isDestroyed = false;

  // Controle de digitação
  private typingTimeout?: NodeJS.Timeout;
  private lastTypingTime = 0;
  private typingUsers: TypingStatus[] = [];

  // Controle de duplicatas
  private processedMessageIds = new Set<string>();
  private messageBuffer: ChatMessage[] = [];

  constructor(
    private appointmentId: string,
    private userId: string,
    private userName: string,
    private userRole: 'DOCTOR' | 'PATIENT'
  ) {
    const config = getSupabaseConfig();
    this.client = createClient(
      config.url,
      config.anonKey,
      {
        realtime: {
          params: {
            eventsPerSecond: 10,
          },
        },
      }
    );

    // Usar o mesmo cliente para todas as operações

    this.setupOnlineStatusMonitoring();
  }

  // Event handlers
  onMessage(handler: MessageHandler): () => void {
    this.messageHandlers.push(handler);
    return () => {
      const index = this.messageHandlers.indexOf(handler);
      if (index > -1) this.messageHandlers.splice(index, 1);
    };
  }

  onStatusChange(handler: StatusHandler): () => void {
    this.statusHandlers.push(handler);
    // Enviar status atual imediatamente
    handler({ ...this.connectionStatus });
    return () => {
      const index = this.statusHandlers.indexOf(handler);
      if (index > -1) this.statusHandlers.splice(index, 1);
    };
  }

  onTypingChange(handler: TypingHandler): () => void {
    this.typingHandlers.push(handler);
    return () => {
      const index = this.typingHandlers.indexOf(handler);
      if (index > -1) this.typingHandlers.splice(index, 1);
    };
  }

  onError(handler: ErrorHandler): () => void {
    this.errorHandlers.push(handler);
    return () => {
      const index = this.errorHandlers.indexOf(handler);
      if (index > -1) this.errorHandlers.splice(index, 1);
    };
  }

  // Conectar ao chat
  async connect(): Promise<void> {
    if (this.isDestroyed) return;

    console.log(`[OptimizedChat] Conectando ao chat ${this.appointmentId}`);
    this.updateStatus('connecting');

    try {
      // Desconectar canal anterior se existir
      if (this.channel) {
        await this.disconnect();
      }

      const channelName = `chat:${this.appointmentId}`;
      this.channel = this.client.channel(channelName, {
        config: {
          presence: { key: this.userId },
          broadcast: { self: true },
        },
      });

      // Configurar listeners
      this.channel
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `appointmentId=eq.${this.appointmentId}`,
        }, (payload: any) => {
          this.handleNewMessage(payload.new);
        })
        .on('broadcast', { event: 'typing' }, (payload: any) => {
          this.handleTypingEvent(payload.payload);
        })
        .on('presence', { event: 'sync' }, () => {
          console.log(`[OptimizedChat] Presença sincronizada`);
        })
        .subscribe((status: string) => {
          this.handleChannelStatus(status);
        });

    } catch (error) {
      console.error('[OptimizedChat] Erro ao conectar:', error);
      this.updateStatus('error', error instanceof Error ? error.message : 'Erro de conexão');
      this.notifyError(error instanceof Error ? error : new Error('Erro de conexão'));
    }
  }

  // Desconectar do chat
  async disconnect(): Promise<void> {
    console.log(`[OptimizedChat] Desconectando do chat ${this.appointmentId}`);

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = undefined;
    }

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
      this.typingTimeout = undefined;
    }

    if (this.channel) {
      try {
        await this.client.removeChannel(this.channel);
      } catch (error) {
        console.warn('[OptimizedChat] Erro ao remover canal:', error);
      }
      this.channel = null;
    }

    this.updateStatus('disconnected');
  }

  // Reconectar
  async reconnect(): Promise<void> {
    if (this.isDestroyed) return;

    console.log(`[OptimizedChat] Reconectando ao chat ${this.appointmentId}`);
    await this.disconnect();
    await this.connect();
  }

  // Buscar mensagens históricas
  async loadMessages(): Promise<ChatMessage[]> {
    try {
      const { data, error } = await this.client
        .from('messages')
        .select('*')
        .eq('appointmentId', this.appointmentId)
        .order('createdAt', { ascending: true });

      if (error) throw error;

      const messages = (data || []).map(msg => this.transformMessage(msg)).filter(Boolean) as ChatMessage[];

      // Limpar cache de IDs processados e reprocessar
      this.processedMessageIds.clear();
      messages.forEach(msg => {
        if (msg?.id) {
          this.processedMessageIds.add(msg.id);
        }
      });

      console.log(`[OptimizedChat] ${messages.length} mensagens carregadas`);
      return messages;
    } catch (error) {
      console.error('[OptimizedChat] Erro ao carregar mensagens:', error);
      throw error;
    }
  }

  // Enviar mensagem de texto
  async sendTextMessage(content: string): Promise<void> {
    if (!content.trim()) {
      throw new Error('Mensagem não pode estar vazia');
    }

    const messageData = {
      id: uuidv4(),
      appointmentId: this.appointmentId,
      senderId: this.userId,
      senderRole: this.userRole,
      type: 'TEXT' as const,
      content: content.trim(),
      createdAt: new Date().toISOString(),
    };

    try {
      const { error } = await this.client
        .from('messages')
        .insert(messageData);

      if (error) throw error;

      console.log('[OptimizedChat] Mensagem de texto enviada');
    } catch (error) {
      console.error('[OptimizedChat] Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  // Enviar mensagem de áudio
  async sendAudioMessage(audioBlob: Blob): Promise<void> {
    try {
      const fileName = `${this.appointmentId}/${Date.now()}_${this.userId}.webm`;
      const arrayBuffer = await audioBlob.arrayBuffer();

      // Upload do arquivo usando cliente admin
      const { data: uploadData, error: uploadError } = await this.client.storage
        .from('chat_audio')
        .upload(fileName, arrayBuffer, {
          contentType: 'audio/webm',
          upsert: true,
        });

      if (uploadError) throw uploadError;

      // Obter URL pública
      const { data: { publicUrl } } = this.client.storage
        .from('chat_audio')
        .getPublicUrl(fileName);

      const messageData = {
        id: uuidv4(),
        appointmentId: this.appointmentId,
        senderId: this.userId,
        senderRole: this.userRole,
        type: 'AUDIO' as const,
        content: publicUrl,
        metadata: {
          fileName,
          contentType: 'audio/webm',
          duration: 0,
          fileSize: audioBlob.size,
          fileUrl: publicUrl,
        },
        createdAt: new Date().toISOString(),
      };

      const { error } = await this.client
        .from('messages')
        .insert(messageData);

      if (error) throw error;

      console.log('[OptimizedChat] Mensagem de áudio enviada');
    } catch (error) {
      console.error('[OptimizedChat] Erro ao enviar áudio:', error);
      throw error;
    }
  }

  // Enviar arquivo
  async sendFileMessage(file: File): Promise<void> {
    try {
      const fileName = `${this.appointmentId}/${Date.now()}_${file.name}`;
      const arrayBuffer = await file.arrayBuffer();

      // Upload do arquivo usando cliente admin
      const { data: uploadData, error: uploadError } = await this.client.storage
        .from('chat_attachments')
        .upload(fileName, arrayBuffer, {
          contentType: file.type,
          upsert: true,
        });

      if (uploadError) throw uploadError;

      // Obter URL pública
      const { data: { publicUrl } } = this.client.storage
        .from('chat_attachments')
        .getPublicUrl(fileName);

      const messageData = {
        id: uuidv4(),
        appointmentId: this.appointmentId,
        senderId: this.userId,
        senderRole: this.userRole,
        type: 'FILE' as const,
        content: publicUrl,
        metadata: {
          fileName: file.name,
          contentType: file.type,
          size: file.size,
          path: fileName,
          fileUrl: publicUrl,
        },
        createdAt: new Date().toISOString(),
      };

      const { error } = await this.client
        .from('messages')
        .insert(messageData);

      if (error) throw error;

      console.log('[OptimizedChat] Arquivo enviado');
    } catch (error) {
      console.error('[OptimizedChat] Erro ao enviar arquivo:', error);
      throw error;
    }
  }

  // Controle de digitação
  startTyping(): void {
    const now = Date.now();

    // Evitar envios muito frequentes
    if (now - this.lastTypingTime < 1000) return;

    this.lastTypingTime = now;
    this.sendTypingIndicator(true);

    // Auto-parar digitação após 3 segundos
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    this.typingTimeout = setTimeout(() => {
      this.stopTyping();
    }, 3000);
  }

  stopTyping(): void {
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
      this.typingTimeout = undefined;
    }

    this.sendTypingIndicator(false);
  }

  // Handlers privados
  private handleNewMessage(messageData: any): void {
    if (!messageData?.id) return;

    // Evitar duplicatas
    if (this.processedMessageIds.has(messageData.id)) {
      return;
    }

    this.processedMessageIds.add(messageData.id);
    const message = this.transformMessage(messageData);

    if (message) {
      this.messageHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('[OptimizedChat] Erro no handler de mensagem:', error);
        }
      });
    }
  }

  private handleTypingEvent(payload: any): void {
    if (!payload?.userId || payload.userId === this.userId) return;

    const typingStatus: TypingStatus = {
      userId: payload.userId,
      userName: payload.userName || 'Usuário',
      isTyping: payload.isTyping,
      timestamp: payload.timestamp || Date.now(),
    };

    if (typingStatus.isTyping) {
      // Adicionar ou atualizar usuário digitando
      const existingIndex = this.typingUsers.findIndex(t => t.userId === typingStatus.userId);
      if (existingIndex >= 0) {
        this.typingUsers[existingIndex] = typingStatus;
      } else {
        this.typingUsers.push(typingStatus);
      }
    } else {
      // Remover usuário da lista de digitação
      this.typingUsers = this.typingUsers.filter(t => t.userId !== typingStatus.userId);
    }

    this.typingHandlers.forEach(handler => {
      try {
        handler([...this.typingUsers]);
      } catch (error) {
        console.error('[OptimizedChat] Erro no handler de digitação:', error);
      }
    });
  }

  private handleChannelStatus(status: string): void {
    console.log(`[OptimizedChat] Status do canal: ${status}`);

    switch (status) {
      case 'SUBSCRIBED':
        this.updateStatus('connected');
        this.connectionStatus.reconnectAttempts = 0;
        break;
      case 'CHANNEL_ERROR':
        this.updateStatus('error', 'Erro no canal');
        this.scheduleReconnect();
        break;
      case 'TIMED_OUT':
        this.updateStatus('error', 'Timeout na conexão');
        this.scheduleReconnect();
        break;
      case 'CLOSED':
        this.updateStatus('disconnected');
        break;
    }
  }

  private updateStatus(status: ConnectionStatus['status'], error?: string): void {
    const previousStatus = this.connectionStatus.status;

    this.connectionStatus.status = status;
    this.connectionStatus.isOnline = navigator?.onLine ?? true;

    if (error) {
      this.connectionStatus.lastError = error;
    }

    if (status === 'connected') {
      this.connectionStatus.lastConnected = new Date();
      this.connectionStatus.lastError = undefined;
    }

    // Notificar apenas se o status mudou
    if (previousStatus !== status) {
      console.log(`[OptimizedChat] Status atualizado: ${previousStatus} -> ${status}`);
      this.statusHandlers.forEach(handler => {
        try {
          handler({ ...this.connectionStatus });
        } catch (error) {
          console.error('[OptimizedChat] Erro no handler de status:', error);
        }
      });
    }
  }

  private scheduleReconnect(): void {
    if (this.isDestroyed) return;

    if (this.connectionStatus.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn('[OptimizedChat] Máximo de tentativas excedido');
      this.updateStatus('error', 'Máximo de tentativas de reconexão excedido');
      return;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.connectionStatus.reconnectAttempts += 1;
    const delay = this.baseReconnectDelay * Math.pow(2, this.connectionStatus.reconnectAttempts - 1);

    console.log(`[OptimizedChat] Agendando reconexão em ${delay}ms (tentativa ${this.connectionStatus.reconnectAttempts})`);

    this.updateStatus('reconnecting');

    this.reconnectTimeout = setTimeout(async () => {
      try {
        await this.reconnect();
      } catch (error) {
        console.error('[OptimizedChat] Erro na reconexão:', error);
        if (this.connectionStatus.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        } else {
          this.updateStatus('error', 'Falha na reconexão após múltiplas tentativas');
        }
      }
    }, delay);
  }

  private sendTypingIndicator(isTyping: boolean): void {
    if (!this.channel) return;

    const payload = {
      userId: this.userId,
      userName: this.userName,
      isTyping,
      timestamp: Date.now(),
    };

    this.channel.send({
      type: 'broadcast',
      event: 'typing',
      payload,
    }).catch((error: any) => {
      console.warn('[OptimizedChat] Erro ao enviar indicador de digitação:', error);
    });
  }

  private transformMessage(dbMessage: any): ChatMessage | null {
    if (!dbMessage) return null;

    // Extrair campos de arquivo do metadata se existirem
    const metadata = dbMessage.metadata || {};
    const file_url = metadata.fileUrl || dbMessage.file_url;
    const file_name = metadata.fileName || dbMessage.file_name;
    const file_size = metadata.fileSize || metadata.size || dbMessage.file_size;

    return {
      id: dbMessage.id,
      appointmentId: dbMessage.appointmentId,
      senderId: dbMessage.senderId,
      senderRole: dbMessage.senderRole,
      type: dbMessage.type,
      content: dbMessage.content,
      metadata: dbMessage.metadata,
      createdAt: dbMessage.createdAt,
      file_url,
      file_name,
      file_size,
    };
  }

  private setupOnlineStatusMonitoring(): void {
    if (typeof window === 'undefined') return;

    const handleOnlineStatusChange = () => {
      const wasOnline = this.connectionStatus.isOnline;
      this.connectionStatus.isOnline = navigator.onLine;

      if (!wasOnline && this.connectionStatus.isOnline) {
        console.log('[OptimizedChat] Conexão online restaurada');
        if (this.connectionStatus.status === 'disconnected') {
          this.reconnect();
        }
      }
    };

    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);
  }

  private notifyError(error: Error): void {
    this.errorHandlers.forEach(handler => {
      try {
        handler(error);
      } catch (err) {
        console.error('[OptimizedChat] Erro no handler de erro:', err);
      }
    });
  }

  // Obter status da conexão
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  // Destruir serviço
  async destroy(): Promise<void> {
    console.log('[OptimizedChat] Destruindo serviço...');
    this.isDestroyed = true;

    await this.disconnect();

    this.messageHandlers.length = 0;
    this.statusHandlers.length = 0;
    this.typingHandlers.length = 0;
    this.errorHandlers.length = 0;

    this.processedMessageIds.clear();
    this.messageBuffer.length = 0;
    this.typingUsers.length = 0;
  }
}
