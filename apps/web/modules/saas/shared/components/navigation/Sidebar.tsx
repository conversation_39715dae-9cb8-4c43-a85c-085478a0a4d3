'use client';
// import { siteConfig } from "@/app/siteConfig"
// import { Tooltip } from "@/components/Tooltip"
// import { cx, focusRing } from "@/lib/utils"
import {
	Calendar1Icon,
	CalendarClock,
	HelpCircleIcon,
	LayoutDashboardIcon,
	PanelRightClose,
	PanelRightOpen,
	Stethoscope,
	TagsIcon,
	UserRoundCog,
	UsersIcon,
	Settings,
	BanknoteIcon,
	LifeBuoy,
	Siren,
	FolderSync,
	BrainIcon,
	BotIcon,
	CreditCard,
	Receipt,
	Building2,
} from 'lucide-react';

import { siteConfig } from '@saas/shared/siteConfig';
import { type User, UserRoleSchema } from 'database';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { Logo } from '@shared/components/Logo';
import { Tooltip } from './Tooltip';
import { UserProfileDesktop } from './UserProfile';
import { cx } from './utils';
import { IconMessageCirclePlus, IconRobot, IconRobotFace, IconSubscript } from '@tabler/icons-react';
import { FaRobot } from 'react-icons/fa6';
import { RiRobotLine } from '@remixicon/react';

interface SidebarProps {
	isCollapsed: boolean;
	toggleSidebar: () => void;
	user: User;
}

export function Sidebar({ isCollapsed, toggleSidebar, user }: SidebarProps) {
	// Validações de role simplificadas
	const isAdmin = user?.role === UserRoleSchema.Values.ADMIN;
	const isDoctor = user?.role === UserRoleSchema.Values.DOCTOR;
	const isPatient = user?.role === UserRoleSchema.Values.PATIENT;
	const isSecretary = user?.role === UserRoleSchema.Values.SECRETARY;

	const pathname = usePathname();
	const isActive = (itemHref: string) => {
		if (!pathname) return false;
		if (itemHref === siteConfig.baseLinks.settings.audit) {
			return pathname.startsWith('/settings');
		}
		// Special handling for finance pages - only active for exact match or direct subpages
		if (itemHref === '/app/finance') {
			return pathname === '/app/finance' || pathname === '/app/finance/admin' || pathname === '/app/finance/doctor';
		}
		return pathname === itemHref || pathname.startsWith(itemHref);
	};

	const navigation = [
		// === ATENDIMENTO AO PACIENTE ===
		{
			name: 'Dashboard',
			href: '/app/dashboard',
			icon: LayoutDashboardIcon,
			show: true, // Todos veem
			category: 'atendimento',
		},
		{
			name: 'Plantão Médico',
			href: "/app/plantao",
			icon: Siren,
			show: isDoctor, // Apenas médicos
			category: 'atendimento',
		},
		{
			name: 'ZapChat',
			href: '/app/zapchat',
			icon: IconMessageCirclePlus,
			show: true, // Todos podem usar o chat
			category: 'atendimento',
		},
		{
			name: 'Assistente IA',
			href: '/app/doctor/agent',
			icon: IconRobotFace,
			show: isDoctor || isAdmin, // Apenas médicos e admins
			category: 'atendimento',
		},

		// === GESTÃO CLÍNICA ===
		{
			name: 'Agendamento',
			href: '/app/schedule',
			icon: Calendar1Icon,
			show: isDoctor || isSecretary || isAdmin, // Médicos, secretárias e admins
			category: 'gestao',
		},
		{
			name: 'Pacientes',
			href: '/app/patients',
			icon: UsersIcon,
			show: isDoctor || isSecretary || isAdmin, // Médicos, secretárias e admins
			category: 'gestao',
		},
		{
			name: 'Médicos',
			href: '/app/doctors',
			icon: Stethoscope,
			show: isAdmin, // Apenas admins
			category: 'gestao',
		},
		{
			name: 'Especialidades',
			href: '/app/specialties',
			icon: TagsIcon,
			show: isAdmin, // Apenas admins
			category: 'gestao',
		},
		{
			name: 'Prescrições',
			href: '/app/prescriptions',
			icon: Receipt,
			show: isDoctor || isAdmin, // Médicos e admins
			category: 'gestao',
		},

		// === FINANCEIRO ===
		{
			name: 'Financeiro',
			href: '/app/finance',
			icon: BanknoteIcon,
			show: isAdmin || isDoctor, // Admins e médicos (para ver seus ganhos)
			category: 'financeiro',
		},
		{
			name: 'Pagamentos',
			href: '/app/finance/payments',
			icon: Receipt,
			show: isAdmin || isDoctor, // Admins e médicos podem ver pagamentos
			category: 'financeiro',
		},
		{
			name: 'Assinaturas',
			href: '/app/finance/subscriptions',
			icon: CreditCard,
			show: isAdmin, // Apenas admins podem gerenciar assinaturas
			category: 'financeiro',
		},

		// === ADMINISTRAÇÃO ===
		{
			name: 'Administração',
			href: '/app/admin',
			icon: Settings,
			show: isAdmin, // Apenas admins
			category: 'administracao',
		},
		{
			name: 'Configurações',
			href: '/app/settings',
			icon: UserRoundCog,
			show: true, // Todos podem configurar seu perfil
			category: 'administracao',
		},
		{
			name: 'Ajuda',
			href: '/app/help',
			icon: HelpCircleIcon,
			show: true, // Todos podem acessar ajuda
			category: 'administracao',
		},
	] as const;

	// Agrupar itens por categoria
	const groupedNavigation = navigation.reduce((acc, item) => {
		if (!acc[item.category]) {
			acc[item.category] = [];
		}
		acc[item.category].push(item);
		return acc;
	}, {} as Record<string, Array<typeof navigation[number]>>);

	const categoryLabels = {
		atendimento: 'Atendimento',
		gestao: 'Gestão Clínica',
		financeiro: 'Financeiro',
		administracao: 'Administração',
	};

	// Função para verificar se uma categoria tem itens visíveis
	const hasVisibleItems = (category: string) => {
		return groupedNavigation[category]?.some(item => item.show) ?? false;
	};

	return (
		<>
			{/* Desktop sidebar (visible only on desktop) */}
			<nav
				className={cx(
					isCollapsed ? 'lg:w-[60px]' : 'lg:w-64',
					'hidden overflow-x-hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:flex-col',
					'ease transform-gpu transition-all duration-100 will-change-transform'
				)}
			>
				<aside className='flex grow flex-col gap-y-4 overflow-y-auto whitespace-nowrap px-3 py-4'>
					<div>
						<div className='flex justify-between items-center'>
							{isCollapsed ? <Logo isCompact={true} /> : <Logo />}
							<button
								className='group inline-flex rounded-md p-2 hover:bg-gray-200/50 hover:dark:bg-gray-900'
								onClick={toggleSidebar}
							>
								{isCollapsed ? (
									<PanelRightClose
										className='size-5 shrink-0 text-gray-500 group-hover:text-gray-700 dark:text-gray-500 group-hover:dark:text-gray-300'
										aria-hidden='true'
									/>
								) : (
									<PanelRightOpen
										className='size-5 shrink-0 text-gray-500 group-hover:text-gray-700 dark:text-gray-500 group-hover:dark:text-gray-300'
										aria-hidden='true'
									/>
								)}
							</button>
						</div>
					</div>

					<nav
						aria-label='core navigation links'
						className='flex flex-1 flex-col space-y-6'
					>
						{Object.entries(groupedNavigation).map(([category, items]) => {
							// Só mostra categorias que têm itens visíveis para o usuário
							if (!hasVisibleItems(category)) return null;

							return (
								<div key={category}>
									{!isCollapsed && (
										<span
											className='block h-6 text-xs font-semibold leading-6 text-gray-600 dark:text-gray-400 uppercase tracking-wide mb-2'
										>
											{categoryLabels[category as keyof typeof categoryLabels]}
										</span>
									)}
									<ul role='list' className='space-y-1'>
										{items
											.filter((item) => item.show)
											.map((item) => (
												<li key={item.name}>
													{isCollapsed ? (
														<Tooltip
															side='right'
															content={item.name}
															sideOffset={6}
															showArrow={false}
															className='z-[999]'
														>
															<Link
																href={item.href}
																className={cx(
																	isActive(item.href)
																		? 'text-highlight dark:highlight bg-highlight/10'
																		: 'text-gray-700 dark:text-gray-300',
																	'inline-flex border-none items-center rounded-md p-2 text-sm font-medium transition hover:bg-gray-200/50 hover:dark:bg-gray-900'
																)}
															>
																<item.icon
																	className='size-5 shrink-0'
																	aria-hidden='true'
																/>
															</Link>
														</Tooltip>
													) : (
														<Link
															href={item.href}
															className={cx(
																isActive(item.href)
																	? 'text-highlight dark:highlight bg-highlight/10'
																	: 'text-gray-700 dark:text-gray-300',
																'flex border-none items-center gap-x-2.5 rounded-md p-2 text-sm font-medium transition-opacity hover:bg-gray-200/50 hover:dark:bg-gray-900'
															)}
														>
															<item.icon
																className='size-5 shrink-0'
																aria-hidden='true'
															/>
															{item.name}
														</Link>
													)}
												</li>
											))}
									</ul>
								</div>
							);
						})}
					</nav>
					<div className='mt-auto border-t border-gray-200 pt-3 dark:border-gray-800'>
						<UserProfileDesktop isCollapsed={isCollapsed} />
					</div>
				</aside>
			</nav>
		</>
	);
}
